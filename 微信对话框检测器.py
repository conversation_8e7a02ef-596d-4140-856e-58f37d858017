"""
微信对话框检测器 - 专门检测微信单独对话框窗口
"""
import pygetwindow as gw
import pyautogui
import time
import json
import os
from datetime import datetime
from typing import List, Dict, Optional
import tkinter as tk
from tkinter import ttk, messagebox
import threading

class WeChatDialogDetector:
    def __init__(self):
        self.detected_dialogs = []
        self.config_file = "dialog_windows.json"
        self.load_config()
    
    def load_config(self):
        """加载已保存的对话框配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.detected_dialogs = data.get('dialog_windows', [])
        except Exception as e:
            print(f"加载配置失败: {e}")
            self.detected_dialogs = []
    
    def save_config(self):
        """保存对话框配置"""
        try:
            config = {
                'dialog_windows': self.detected_dialogs,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")
    
    def detect_all_wechat_windows(self) -> List[Dict]:
        """检测所有微信相关窗口"""
        print("🔍 正在检测所有微信相关窗口...")
        
        all_windows = gw.getAllWindows()
        wechat_windows = []
        
        for window in all_windows:
            title = window.title.strip()
            
            # 检测包含微信相关内容的窗口
            if (title and 
                window.visible and 
                window.width > 200 and 
                window.height > 200 and
                not window.isMinimized):
                
                # 检查是否为微信相关窗口
                if self._is_wechat_related(title, window):
                    window_info = {
                        'title': title,
                        'width': window.width,
                        'height': window.height,
                        'left': window.left,
                        'top': window.top,
                        'type': self._classify_window_type(title, window),
                        'detected_time': datetime.now().isoformat()
                    }
                    wechat_windows.append(window_info)
        
        return wechat_windows
    
    def _is_wechat_related(self, title: str, window) -> bool:
        """判断是否为微信相关窗口"""
        # 明确的微信窗口标识
        wechat_indicators = [
            "微信",
            "WeChat",
            # 可能的对话框标题模式
        ]
        
        # 检查标题是否包含微信标识
        for indicator in wechat_indicators:
            if indicator in title:
                return True
        
        # 检查窗口大小和位置特征
        # 微信对话框通常有特定的尺寸范围
        if (300 <= window.width <= 1200 and 
            400 <= window.height <= 900):
            
            # 进一步检查窗口内容（通过截图）
            if self._check_window_content(window):
                return True
        
        return False
    
    def _check_window_content(self, window) -> bool:
        """通过截图检查窗口内容是否为微信对话框"""
        try:
            # 截取窗口
            screenshot = pyautogui.screenshot(
                region=(window.left, window.top, window.width, window.height)
            )
            
            # 这里可以添加更复杂的图像识别逻辑
            # 比如检查是否有微信特有的UI元素
            
            return True  # 暂时返回True，后续可以添加更精确的检测
            
        except Exception as e:
            print(f"检查窗口内容失败: {e}")
            return False
    
    def _classify_window_type(self, title: str, window) -> str:
        """分类窗口类型"""
        if title == "微信":
            return "主窗口"
        elif "微信" in title and title != "微信":
            return "对话框"
        else:
            return "未知"
    
    def find_dialog_windows(self) -> List[Dict]:
        """查找对话框窗口"""
        all_windows = self.detect_all_wechat_windows()
        dialog_windows = [w for w in all_windows if w['type'] == '对话框']
        
        print(f"✅ 找到 {len(dialog_windows)} 个对话框窗口:")
        for i, dialog in enumerate(dialog_windows):
            print(f"  {i+1}. {dialog['title']} ({dialog['width']}x{dialog['height']})")
        
        return dialog_windows
    
    def add_dialog_to_monitor(self, title: str):
        """添加对话框到监控列表"""
        # 检查是否已存在
        for dialog in self.detected_dialogs:
            if dialog['title'] == title:
                dialog['last_seen'] = datetime.now().isoformat()
                dialog['enabled'] = True
                self.save_config()
                return
        
        # 添加新的对话框
        new_dialog = {
            'title': title,
            'enabled': True,
            'added_time': datetime.now().isoformat(),
            'last_seen': datetime.now().isoformat()
        }
        self.detected_dialogs.append(new_dialog)
        self.save_config()
        print(f"✅ 已添加对话框到监控: {title}")
    
    def remove_dialog_from_monitor(self, title: str):
        """从监控列表移除对话框"""
        self.detected_dialogs = [d for d in self.detected_dialogs if d['title'] != title]
        self.save_config()
        print(f"✅ 已移除对话框: {title}")
    
    def get_monitored_dialogs(self) -> List[str]:
        """获取当前监控的对话框标题列表"""
        return [d['title'] for d in self.detected_dialogs if d.get('enabled', True)]
    
    def update_window_config(self):
        """更新主程序的窗口配置"""
        try:
            monitored_titles = self.get_monitored_dialogs()
            
            # 更新window_titles.json
            window_config = {
                'monitored_titles': self.detected_dialogs,
                'last_updated': datetime.now().isoformat()
            }
            
            with open('window_titles.json', 'w', encoding='utf-8') as f:
                json.dump(window_config, f, ensure_ascii=False, indent=2)
            
            # 更新window_config.json
            if monitored_titles:
                main_config = {
                    'WECHAT_WINDOW_TITLES': monitored_titles,
                    'PRIMARY_TITLE': monitored_titles[0]
                }
                
                with open('window_config.json', 'w', encoding='utf-8') as f:
                    json.dump(main_config, f, ensure_ascii=False, indent=2)
            
            print("✅ 主程序配置已更新")
            
        except Exception as e:
            print(f"❌ 更新配置失败: {e}")

class WeChatDialogGUI:
    def __init__(self):
        self.detector = WeChatDialogDetector()
        self.root = tk.Tk()
        self.root.title("微信对话框检测器")
        self.root.geometry("800x600")
        
        self.setup_ui()
        self.refresh_windows()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="微信对话框检测器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 检测到的窗口列表
        ttk.Label(main_frame, text="检测到的微信窗口:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        
        # 窗口列表
        columns = ('标题', '类型', '大小', '状态')
        self.windows_tree = ttk.Treeview(main_frame, columns=columns, show='tree headings', height=10)
        
        for col in columns:
            self.windows_tree.heading(col, text=col)
            self.windows_tree.column(col, width=150)
        
        self.windows_tree.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.windows_tree.yview)
        scrollbar.grid(row=2, column=3, sticky=(tk.N, tk.S))
        self.windows_tree.configure(yscrollcommand=scrollbar.set)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=10)
        
        ttk.Button(button_frame, text="🔍 刷新窗口", command=self.refresh_windows).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="➕ 添加到监控", command=self.add_to_monitor).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="➖ 移除监控", command=self.remove_from_monitor).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="💾 更新配置", command=self.update_config).pack(side=tk.LEFT, padx=5)
        
        # 监控列表
        ttk.Label(main_frame, text="当前监控的对话框:").grid(row=4, column=0, sticky=tk.W, pady=(20, 5))
        
        self.monitor_listbox = tk.Listbox(main_frame, height=6)
        self.monitor_listbox.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var)
        status_label.grid(row=6, column=0, columnspan=3, sticky=tk.W)
    
    def refresh_windows(self):
        """刷新窗口列表"""
        self.status_var.set("🔍 正在检测窗口...")
        self.root.update()
        
        try:
            # 清空列表
            for item in self.windows_tree.get_children():
                self.windows_tree.delete(item)
            
            # 检测窗口
            windows = self.detector.detect_all_wechat_windows()
            
            # 显示窗口
            for i, window in enumerate(windows):
                self.windows_tree.insert("", "end", text=str(i+1), values=(
                    window['title'],
                    window['type'],
                    f"{window['width']}x{window['height']}",
                    "检测到"
                ))
            
            # 更新监控列表
            self.update_monitor_list()
            
            self.status_var.set(f"✅ 找到 {len(windows)} 个微信窗口")
            
        except Exception as e:
            self.status_var.set(f"❌ 检测失败: {e}")
    
    def update_monitor_list(self):
        """更新监控列表显示"""
        self.monitor_listbox.delete(0, tk.END)
        for dialog in self.detector.detected_dialogs:
            status = "✅" if dialog.get('enabled', True) else "❌"
            self.monitor_listbox.insert(tk.END, f"{status} {dialog['title']}")
    
    def add_to_monitor(self):
        """添加选中的窗口到监控"""
        selection = self.windows_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个窗口")
            return
        
        item = self.windows_tree.item(selection[0])
        title = item['values'][0]
        
        self.detector.add_dialog_to_monitor(title)
        self.update_monitor_list()
        self.status_var.set(f"✅ 已添加 {title} 到监控")
    
    def remove_from_monitor(self):
        """从监控移除选中的对话框"""
        selection = self.monitor_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个监控项")
            return
        
        index = selection[0]
        dialog = self.detector.detected_dialogs[index]
        title = dialog['title']
        
        self.detector.remove_dialog_from_monitor(title)
        self.update_monitor_list()
        self.status_var.set(f"✅ 已移除 {title} 的监控")
    
    def update_config(self):
        """更新主程序配置"""
        try:
            self.detector.update_window_config()
            self.status_var.set("✅ 配置已更新")
            messagebox.showinfo("成功", "主程序配置已更新！")
        except Exception as e:
            self.status_var.set(f"❌ 更新失败: {e}")
            messagebox.showerror("错误", f"更新配置失败: {e}")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    print("🔧 微信对话框检测器")
    print("=" * 50)
    
    while True:
        print("\n请选择操作:")
        print("1. 🔍 检测所有微信窗口")
        print("2. 📋 查找对话框窗口")
        print("3. ➕ 添加对话框到监控")
        print("4. 📝 查看当前监控列表")
        print("5. 💾 更新主程序配置")
        print("6. 🖥️ 打开图形界面")
        print("7. ❌ 退出")
        
        choice = input("\n请输入选择 (1-7): ").strip()
        
        detector = WeChatDialogDetector()
        
        if choice == "1":
            windows = detector.detect_all_wechat_windows()
            print(f"\n✅ 检测到 {len(windows)} 个微信窗口:")
            for i, window in enumerate(windows):
                print(f"  {i+1}. {window['title']} - {window['type']} ({window['width']}x{window['height']})")
        
        elif choice == "2":
            dialogs = detector.find_dialog_windows()
            if not dialogs:
                print("❌ 未找到对话框窗口")
        
        elif choice == "3":
            dialogs = detector.find_dialog_windows()
            if dialogs:
                print("\n请选择要添加的对话框:")
                for i, dialog in enumerate(dialogs):
                    print(f"  {i+1}. {dialog['title']}")
                
                try:
                    index = int(input("请输入序号: ")) - 1
                    if 0 <= index < len(dialogs):
                        detector.add_dialog_to_monitor(dialogs[index]['title'])
                    else:
                        print("❌ 无效的序号")
                except ValueError:
                    print("❌ 请输入有效的数字")
            else:
                print("❌ 未找到对话框窗口")
        
        elif choice == "4":
            monitored = detector.get_monitored_dialogs()
            if monitored:
                print(f"\n📋 当前监控 {len(monitored)} 个对话框:")
                for i, title in enumerate(monitored):
                    print(f"  {i+1}. {title}")
            else:
                print("❌ 当前没有监控任何对话框")
        
        elif choice == "5":
            detector.update_window_config()
        
        elif choice == "6":
            gui = WeChatDialogGUI()
            gui.run()
            break
        
        elif choice == "7":
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效的选择，请重新输入")

if __name__ == "__main__":
    main()
