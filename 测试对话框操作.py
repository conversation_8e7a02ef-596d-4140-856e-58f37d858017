"""
测试微信对话框检测和操作功能
"""
import time
import logging
from window_controller import WindowController
from 微信对话框检测器 import WeChatDialogDetector

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_dialog_detection():
    """测试对话框检测功能"""
    print("🔍 测试微信对话框检测功能")
    print("=" * 50)
    
    detector = WeChatDialogDetector()
    
    # 检测所有微信窗口
    print("\n1. 检测所有微信相关窗口:")
    all_windows = detector.detect_all_wechat_windows()
    
    if all_windows:
        for i, window in enumerate(all_windows):
            print(f"   {i+1}. {window['title']} - {window['type']}")
            print(f"      大小: {window['width']}x{window['height']}")
            print(f"      位置: ({window['left']}, {window['top']})")
    else:
        print("   ❌ 未检测到微信窗口")
    
    # 查找对话框窗口
    print("\n2. 查找对话框窗口:")
    dialog_windows = detector.find_dialog_windows()
    
    if dialog_windows:
        print(f"   ✅ 找到 {len(dialog_windows)} 个对话框")
        for dialog in dialog_windows:
            print(f"   - {dialog['title']}")
    else:
        print("   ❌ 未找到对话框窗口")
    
    # 显示当前监控列表
    print("\n3. 当前监控的对话框:")
    monitored = detector.get_monitored_dialogs()
    if monitored:
        for title in monitored:
            print(f"   - {title}")
    else:
        print("   ❌ 当前没有监控任何对话框")
    
    return detector, dialog_windows

def test_window_controller():
    """测试窗口控制器功能"""
    print("\n🎮 测试窗口控制器功能")
    print("=" * 50)
    
    controller = WindowController()
    
    # 查找微信窗口
    print("\n1. 查找微信窗口:")
    window = controller.find_wechat_window()
    
    if window:
        print(f"   ✅ 找到窗口: {window.title}")
        print(f"   大小: {window.width}x{window.height}")
        print(f"   位置: ({window.left}, {window.top})")
        print(f"   状态: {'最小化' if window.isMinimized else '正常'}")
    else:
        print("   ❌ 未找到微信窗口")
        return None
    
    # 测试窗口截图
    print("\n2. 测试窗口截图:")
    screenshot = controller.capture_window()
    if screenshot:
        print(f"   ✅ 截图成功，大小: {screenshot.size}")
        # 保存截图用于调试
        screenshot.save("debug_window_screenshot.png")
        print("   💾 截图已保存为 debug_window_screenshot.png")
    else:
        print("   ❌ 截图失败")
    
    # 测试聊天区域截图
    print("\n3. 测试聊天区域截图:")
    chat_screenshot = controller.capture_chat_area()
    if chat_screenshot:
        print(f"   ✅ 聊天区域截图成功，大小: {chat_screenshot.size}")
        chat_screenshot.save("debug_chat_area.png")
        print("   💾 聊天区域截图已保存为 debug_chat_area.png")
    else:
        print("   ❌ 聊天区域截图失败")
    
    return controller

def test_message_sending(controller):
    """测试消息发送功能"""
    print("\n📤 测试消息发送功能")
    print("=" * 50)
    
    if not controller:
        print("❌ 窗口控制器未初始化")
        return
    
    # 询问用户是否要测试发送消息
    response = input("\n⚠️ 是否要测试发送消息？这将在微信中发送一条测试消息 (y/N): ").strip().lower()
    
    if response == 'y':
        test_message = "🤖 这是一条自动化测试消息，请忽略"
        
        print(f"\n📝 准备发送测试消息: {test_message}")
        print("⏰ 3秒后开始发送，请确保微信窗口处于正确的聊天界面...")
        
        for i in range(3, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
        
        print("🚀 开始发送消息...")
        success = controller.send_message(test_message)
        
        if success:
            print("✅ 消息发送成功！")
        else:
            print("❌ 消息发送失败")
    else:
        print("⏭️ 跳过消息发送测试")

def test_input_position_detection(controller):
    """测试输入框位置检测"""
    print("\n🎯 测试输入框位置检测")
    print("=" * 50)
    
    if not controller:
        print("❌ 窗口控制器未初始化")
        return
    
    # 测试输入框位置估算
    input_position = controller._estimate_input_position()
    if input_position:
        x, y = input_position
        print(f"✅ 估算输入框位置: ({x}, {y})")
        
        # 询问是否要可视化位置
        response = input("是否要在屏幕上标记输入框位置？(y/N): ").strip().lower()
        if response == 'y':
            import pyautogui
            
            print("🎯 将在输入框位置显示红点标记...")
            print("⏰ 3秒后显示标记...")
            
            for i in range(3, 0, -1):
                print(f"   {i}...")
                time.sleep(1)
            
            # 在输入框位置点击（不发送消息）
            try:
                pyautogui.click(x, y)
                print("✅ 已在估算位置点击")
            except Exception as e:
                print(f"❌ 点击失败: {e}")
    else:
        print("❌ 无法估算输入框位置")

def interactive_dialog_management():
    """交互式对话框管理"""
    print("\n🛠️ 交互式对话框管理")
    print("=" * 50)
    
    detector = WeChatDialogDetector()
    
    while True:
        print("\n请选择操作:")
        print("1. 🔍 刷新检测微信窗口")
        print("2. ➕ 添加对话框到监控")
        print("3. ➖ 移除对话框监控")
        print("4. 📋 查看监控列表")
        print("5. 💾 更新主程序配置")
        print("6. 🔙 返回主菜单")
        
        choice = input("\n请输入选择 (1-6): ").strip()
        
        if choice == "1":
            windows = detector.detect_all_wechat_windows()
            print(f"\n✅ 检测到 {len(windows)} 个微信窗口:")
            for i, window in enumerate(windows):
                print(f"   {i+1}. {window['title']} - {window['type']}")
        
        elif choice == "2":
            dialogs = detector.find_dialog_windows()
            if dialogs:
                print("\n请选择要添加的对话框:")
                for i, dialog in enumerate(dialogs):
                    print(f"   {i+1}. {dialog['title']}")
                
                try:
                    index = int(input("请输入序号: ")) - 1
                    if 0 <= index < len(dialogs):
                        detector.add_dialog_to_monitor(dialogs[index]['title'])
                        print("✅ 添加成功")
                    else:
                        print("❌ 无效的序号")
                except ValueError:
                    print("❌ 请输入有效的数字")
            else:
                print("❌ 未找到对话框窗口")
        
        elif choice == "3":
            monitored = detector.get_monitored_dialogs()
            if monitored:
                print("\n当前监控的对话框:")
                for i, title in enumerate(monitored):
                    print(f"   {i+1}. {title}")
                
                try:
                    index = int(input("请输入要移除的序号: ")) - 1
                    if 0 <= index < len(monitored):
                        detector.remove_dialog_from_monitor(monitored[index])
                        print("✅ 移除成功")
                    else:
                        print("❌ 无效的序号")
                except ValueError:
                    print("❌ 请输入有效的数字")
            else:
                print("❌ 当前没有监控任何对话框")
        
        elif choice == "4":
            monitored = detector.get_monitored_dialogs()
            if monitored:
                print(f"\n📋 当前监控 {len(monitored)} 个对话框:")
                for i, title in enumerate(monitored):
                    print(f"   {i+1}. {title}")
            else:
                print("❌ 当前没有监控任何对话框")
        
        elif choice == "5":
            detector.update_window_config()
            print("✅ 配置已更新")
        
        elif choice == "6":
            break
        
        else:
            print("❌ 无效的选择")

def main():
    """主函数"""
    print("🧪 微信对话框操作测试工具")
    print("=" * 60)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 🔍 测试对话框检测")
        print("2. 🎮 测试窗口控制器")
        print("3. 📤 测试消息发送")
        print("4. 🎯 测试输入框定位")
        print("5. 🛠️ 交互式对话框管理")
        print("6. 🔄 完整测试流程")
        print("7. ❌ 退出")
        
        choice = input("\n请输入选择 (1-7): ").strip()
        
        if choice == "1":
            test_dialog_detection()
        
        elif choice == "2":
            test_window_controller()
        
        elif choice == "3":
            controller = test_window_controller()
            test_message_sending(controller)
        
        elif choice == "4":
            controller = test_window_controller()
            test_input_position_detection(controller)
        
        elif choice == "5":
            interactive_dialog_management()
        
        elif choice == "6":
            print("\n🔄 开始完整测试流程...")
            
            # 1. 检测对话框
            detector, dialogs = test_dialog_detection()
            
            # 2. 测试窗口控制器
            controller = test_window_controller()
            
            # 3. 测试输入框定位
            test_input_position_detection(controller)
            
            # 4. 可选的消息发送测试
            test_message_sending(controller)
            
            print("\n✅ 完整测试流程结束")
        
        elif choice == "7":
            print("👋 测试结束，再见！")
            break
        
        else:
            print("❌ 无效的选择，请重新输入")

if __name__ == "__main__":
    main()
