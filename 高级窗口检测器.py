"""
高级微信窗口检测器 - 专门检测微信单独对话框
"""
import pygetwindow as gw
import pyautogui
import time
import json
import os
from datetime import datetime
from typing import List, Dict, Optional
import re

class AdvancedWeChatDetector:
    def __init__(self):
        self.all_windows_cache = []
        self.last_scan_time = None
        
    def scan_all_windows(self, force_refresh=False) -> List[Dict]:
        """扫描所有窗口"""
        # 如果缓存有效且不强制刷新，返回缓存
        if (not force_refresh and 
            self.last_scan_time and 
            (datetime.now() - self.last_scan_time).seconds < 5):
            return self.all_windows_cache
        
        print("🔍 正在扫描所有系统窗口...")
        
        all_windows = gw.getAllWindows()
        window_info_list = []
        
        for window in all_windows:
            try:
                title = window.title.strip()
                
                # 过滤掉无效窗口
                if (not title or 
                    not window.visible or 
                    window.width < 100 or 
                    window.height < 100):
                    continue
                
                window_info = {
                    'title': title,
                    'width': window.width,
                    'height': window.height,
                    'left': window.left,
                    'top': window.top,
                    'is_minimized': window.isMinimized,
                    'class_name': getattr(window, '_hWnd', 'Unknown'),
                    'process_name': self._get_process_name(window),
                    'wechat_score': self._calculate_wechat_score(window, title)
                }
                
                window_info_list.append(window_info)
                
            except Exception as e:
                continue
        
        # 按微信相关性排序
        window_info_list.sort(key=lambda x: x['wechat_score'], reverse=True)
        
        self.all_windows_cache = window_info_list
        self.last_scan_time = datetime.now()
        
        return window_info_list
    
    def _get_process_name(self, window) -> str:
        """获取窗口进程名"""
        try:
            # 这里可以添加更复杂的进程检测逻辑
            return "Unknown"
        except:
            return "Unknown"
    
    def _calculate_wechat_score(self, window, title: str) -> int:
        """计算窗口是微信相关的可能性分数"""
        score = 0
        
        # 标题包含"微信"
        if "微信" in title:
            score += 100
        
        # 标题包含"WeChat"
        if "WeChat" in title:
            score += 100
        
        # 窗口大小特征（微信对话框通常的大小范围）
        if 400 <= window.width <= 1200 and 500 <= window.height <= 1000:
            score += 30
        
        # 主窗口大小特征
        if 800 <= window.width <= 1500 and 600 <= window.height <= 1200:
            score += 20
        
        # 检查是否可能是对话框（标题不是"微信"但包含微信相关内容）
        if "微信" in title and title != "微信":
            score += 50
        
        # 检查常见的微信对话框标题模式
        dialog_patterns = [
            r".*\(\d+\)$",  # 群聊格式：群名(人数)
            r"^[^(]+$",     # 单人聊天：纯名字
        ]
        
        for pattern in dialog_patterns:
            if re.match(pattern, title) and len(title) > 1 and len(title) < 50:
                score += 25
        
        return score
    
    def find_wechat_windows(self) -> List[Dict]:
        """查找所有微信相关窗口"""
        all_windows = self.scan_all_windows()
        
        # 筛选微信相关窗口（分数大于0）
        wechat_windows = [w for w in all_windows if w['wechat_score'] > 0]
        
        print(f"✅ 找到 {len(wechat_windows)} 个可能的微信窗口:")
        
        for i, window in enumerate(wechat_windows):
            window_type = self._classify_window_type(window)
            print(f"   {i+1}. {window['title']}")
            print(f"      类型: {window_type}")
            print(f"      大小: {window['width']}x{window['height']}")
            print(f"      分数: {window['wechat_score']}")
            print(f"      状态: {'最小化' if window['is_minimized'] else '正常'}")
            print()
        
        return wechat_windows
    
    def _classify_window_type(self, window: Dict) -> str:
        """分类窗口类型"""
        title = window['title']
        
        if title == "微信":
            return "主窗口"
        elif "微信" in title and title != "微信":
            return "可能的对话框"
        elif window['wechat_score'] > 50:
            return "高可能性微信窗口"
        else:
            return "低可能性微信窗口"
    
    def find_dialog_windows(self) -> List[Dict]:
        """专门查找对话框窗口"""
        wechat_windows = self.find_wechat_windows()
        
        # 筛选对话框窗口
        dialog_windows = []
        for window in wechat_windows:
            window_type = self._classify_window_type(window)
            if "对话框" in window_type or (window['wechat_score'] > 50 and window['title'] != "微信"):
                dialog_windows.append(window)
        
        print(f"\n🎯 专门筛选出 {len(dialog_windows)} 个可能的对话框:")
        for i, dialog in enumerate(dialog_windows):
            print(f"   {i+1}. {dialog['title']} (分数: {dialog['wechat_score']})")
        
        return dialog_windows
    
    def interactive_window_selection(self) -> Optional[str]:
        """交互式窗口选择"""
        print("\n🔍 开始交互式窗口检测...")
        
        while True:
            print("\n请选择操作:")
            print("1. 🔍 扫描所有窗口")
            print("2. 🎯 查找微信相关窗口")
            print("3. 💬 查找对话框窗口")
            print("4. 📋 显示所有窗口（调试用）")
            print("5. ➕ 手动添加窗口标题")
            print("6. ❌ 退出")
            
            choice = input("\n请输入选择 (1-6): ").strip()
            
            if choice == "1":
                windows = self.scan_all_windows(force_refresh=True)
                print(f"✅ 扫描完成，找到 {len(windows)} 个窗口")
            
            elif choice == "2":
                wechat_windows = self.find_wechat_windows()
                if wechat_windows:
                    selected = self._select_window_from_list(wechat_windows)
                    if selected:
                        return selected
            
            elif choice == "3":
                dialog_windows = self.find_dialog_windows()
                if dialog_windows:
                    selected = self._select_window_from_list(dialog_windows)
                    if selected:
                        return selected
                else:
                    print("❌ 未找到对话框窗口")
            
            elif choice == "4":
                self._show_all_windows_debug()
            
            elif choice == "5":
                title = input("请输入窗口标题: ").strip()
                if title:
                    return title
            
            elif choice == "6":
                return None
            
            else:
                print("❌ 无效的选择")
    
    def _select_window_from_list(self, windows: List[Dict]) -> Optional[str]:
        """从窗口列表中选择"""
        if not windows:
            return None
        
        print("\n请选择窗口:")
        for i, window in enumerate(windows):
            print(f"   {i+1}. {window['title']}")
        
        try:
            index = int(input("请输入序号 (0取消): ")) - 1
            if index == -1:
                return None
            if 0 <= index < len(windows):
                selected_title = windows[index]['title']
                print(f"✅ 已选择: {selected_title}")
                return selected_title
            else:
                print("❌ 无效的序号")
                return None
        except ValueError:
            print("❌ 请输入有效的数字")
            return None
    
    def _show_all_windows_debug(self):
        """显示所有窗口（调试用）"""
        windows = self.scan_all_windows(force_refresh=True)
        
        print(f"\n📋 所有窗口列表 (共 {len(windows)} 个):")
        print("-" * 80)
        
        for i, window in enumerate(windows):
            print(f"{i+1:3d}. {window['title'][:50]:<50} "
                  f"({window['width']:4d}x{window['height']:4d}) "
                  f"分数:{window['wechat_score']:3d}")
        
        print("-" * 80)
    
    def save_selected_window(self, title: str):
        """保存选中的窗口到配置"""
        try:
            # 更新window_titles.json
            config_data = {
                'monitored_titles': [
                    {
                        'title': title,
                        'enabled': True,
                        'added_time': datetime.now().isoformat(),
                        'last_seen': datetime.now().isoformat()
                    }
                ],
                'last_updated': datetime.now().isoformat()
            }
            
            with open('window_titles.json', 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            # 更新window_config.json
            main_config = {
                'WECHAT_WINDOW_TITLES': [title],
                'PRIMARY_TITLE': title
            }
            
            with open('window_config.json', 'w', encoding='utf-8') as f:
                json.dump(main_config, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已保存窗口配置: {title}")
            
        except Exception as e:
            print(f"❌ 保存配置失败: {e}")

def main():
    """主函数"""
    print("🔧 高级微信窗口检测器")
    print("=" * 60)
    print("这个工具将帮助您精确找到微信单独对话框窗口")
    print()
    
    detector = AdvancedWeChatDetector()
    
    # 首先进行一次快速扫描
    print("🚀 正在进行初始扫描...")
    wechat_windows = detector.find_wechat_windows()
    
    if not wechat_windows:
        print("⚠️ 未找到明显的微信窗口")
        print("💡 建议:")
        print("   1. 确保微信PC版已打开")
        print("   2. 确保微信窗口未最小化")
        print("   3. 尝试打开一个微信对话框")
        print()
    
    # 开始交互式选择
    selected_title = detector.interactive_window_selection()
    
    if selected_title:
        print(f"\n🎉 您选择了窗口: {selected_title}")
        
        # 询问是否保存配置
        save_choice = input("是否要保存此窗口到监控配置？(y/N): ").strip().lower()
        if save_choice == 'y':
            detector.save_selected_window(selected_title)
            
            print("\n✅ 配置已保存！")
            print("💡 现在您可以:")
            print("   1. 运行主程序进行监控")
            print("   2. 使用测试工具验证配置")
        else:
            print("⏭️ 未保存配置")
    else:
        print("\n❌ 未选择任何窗口")
    
    print("\n👋 检测完成！")

if __name__ == "__main__":
    main()
